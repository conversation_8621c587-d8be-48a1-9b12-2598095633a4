/**
 * @file rf_common.h
 * @brief RF Online Common Definitions and Includes
 * <AUTHOR> from IDA Pro decompilation
 * @date 2025
 * 
 * This header provides common includes, constants, and utility functions
 * used throughout the RF Online server codebase.
 */

#ifndef RF_COMMON_H
#define RF_COMMON_H

/* ========================================================================
 * Standard Library Includes
 * ======================================================================== */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>
#include <time.h>
#include <errno.h>
#include <assert.h>

/* ========================================================================
 * Platform-Specific Includes
 * ======================================================================== */

#ifdef _WIN32
    #include <windows.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <process.h>
    #include <io.h>
    #include <direct.h>
    
    /* Link required libraries for Visual Studio */
    #pragma comment(lib, "ws2_32.lib")
    #pragma comment(lib, "kernel32.lib")
    #pragma comment(lib, "user32.lib")
    #pragma comment(lib, "advapi32.lib")
    
#else
    #include <unistd.h>
    #include <sys/socket.h>
    #include <sys/types.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <pthread.h>
#endif

/* ========================================================================
 * RF Online Type Definitions
 * ======================================================================== */

#include "rf_types.h"

/* ========================================================================
 * Global Constants
 * ======================================================================== */

/* Server Configuration */
#define RF_SERVER_VERSION_MAJOR     1
#define RF_SERVER_VERSION_MINOR     0
#define RF_SERVER_VERSION_PATCH     0

#define RF_MAX_CLIENTS              1000
#define RF_MAX_ZONES                32
#define RF_MAX_NPCS_PER_ZONE        500
#define RF_MAX_ITEMS_PER_PLAYER     200

/* Network Configuration */
#define RF_DEFAULT_PORT             27015
#define RF_MAX_PACKET_SIZE          8192
#define RF_SOCKET_TIMEOUT           30000   /* 30 seconds */
#define RF_HEARTBEAT_INTERVAL       5000    /* 5 seconds */

/* Game Constants */
#define RF_MAX_LEVEL                99
#define RF_MAX_STAT_VALUE           999
#define RF_RESPAWN_TIME             30000   /* 30 seconds */

/* String Limits */
#define RF_MAX_PLAYER_NAME          32
#define RF_MAX_GUILD_NAME           64
#define RF_MAX_CHAT_MESSAGE         256
#define RF_MAX_FILENAME             260

/* ========================================================================
 * Logging and Debug Macros
 * ======================================================================== */

/* Log levels */
typedef enum {
    RF_LOG_TRACE = 0,
    RF_LOG_DEBUG = 1,
    RF_LOG_INFO = 2,
    RF_LOG_WARN = 3,
    RF_LOG_ERROR = 4,
    RF_LOG_FATAL = 5
} rf_log_level_t;

/* Debug build logging */
#ifdef _DEBUG
    #define RF_LOG(level, format, ...) \
        rf_log_message(level, __FILE__, __LINE__, format, ##__VA_ARGS__)
    
    #define RF_TRACE(format, ...) RF_LOG(RF_LOG_TRACE, format, ##__VA_ARGS__)
    #define RF_DEBUG(format, ...) RF_LOG(RF_LOG_DEBUG, format, ##__VA_ARGS__)
    #define RF_ASSERT(condition) assert(condition)
#else
    #define RF_LOG(level, format, ...)
    #define RF_TRACE(format, ...)
    #define RF_DEBUG(format, ...)
    #define RF_ASSERT(condition)
#endif

/* Always-enabled logging */
#define RF_INFO(format, ...)  RF_LOG(RF_LOG_INFO, format, ##__VA_ARGS__)
#define RF_WARN(format, ...)  RF_LOG(RF_LOG_WARN, format, ##__VA_ARGS__)
#define RF_ERROR(format, ...) RF_LOG(RF_LOG_ERROR, format, ##__VA_ARGS__)
#define RF_FATAL(format, ...) RF_LOG(RF_LOG_FATAL, format, ##__VA_ARGS__)

/* ========================================================================
 * Memory Management Macros
 * ======================================================================== */

/* Safe memory allocation with error checking */
#define RF_MALLOC(size) rf_safe_malloc(size, __FILE__, __LINE__)
#define RF_CALLOC(count, size) rf_safe_calloc(count, size, __FILE__, __LINE__)
#define RF_REALLOC(ptr, size) rf_safe_realloc(ptr, size, __FILE__, __LINE__)
#define RF_FREE(ptr) rf_safe_free((void**)&(ptr))

/* ========================================================================
 * String Utility Macros
 * ======================================================================== */

/* Safe string operations */
#ifdef _WIN32
    #define RF_STRCPY(dest, src, size) strcpy_s(dest, size, src)
    #define RF_STRCAT(dest, src, size) strcat_s(dest, size, src)
    #define RF_SPRINTF(dest, size, format, ...) sprintf_s(dest, size, format, ##__VA_ARGS__)
    #define RF_SNPRINTF(dest, size, format, ...) _snprintf_s(dest, size, _TRUNCATE, format, ##__VA_ARGS__)
#else
    #define RF_STRCPY(dest, src, size) strncpy(dest, src, size - 1); dest[size - 1] = '\0'
    #define RF_STRCAT(dest, src, size) strncat(dest, src, size - strlen(dest) - 1)
    #define RF_SPRINTF(dest, size, format, ...) snprintf(dest, size, format, ##__VA_ARGS__)
    #define RF_SNPRINTF(dest, size, format, ...) snprintf(dest, size, format, ##__VA_ARGS__)
#endif

/* ========================================================================
 * Error Handling Macros
 * ======================================================================== */

/* Check result and return on error */
#define RF_CHECK_RESULT(result) \
    do { \
        rf_result_t _res = (result); \
        if (_res != RF_SUCCESS) { \
            RF_ERROR("Operation failed with result: %d", _res); \
            return _res; \
        } \
    } while(0)

/* Check pointer and return error on null */
#define RF_CHECK_NULL(ptr) \
    do { \
        if ((ptr) == NULL) { \
            RF_ERROR("Null pointer detected: %s", #ptr); \
            return RF_ERROR_INVALID_PARAM; \
        } \
    } while(0)

/* ========================================================================
 * Threading Macros (Platform Abstraction)
 * ======================================================================== */

#ifdef _WIN32
    typedef HANDLE rf_thread_t;
    typedef CRITICAL_SECTION rf_mutex_t;
    typedef HANDLE rf_event_t;
    
    #define RF_THREAD_CREATE(thread, func, arg) \
        (*(thread) = CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)(func), (arg), 0, NULL))
    
    #define RF_MUTEX_INIT(mutex) InitializeCriticalSection(mutex)
    #define RF_MUTEX_LOCK(mutex) EnterCriticalSection(mutex)
    #define RF_MUTEX_UNLOCK(mutex) LeaveCriticalSection(mutex)
    #define RF_MUTEX_DESTROY(mutex) DeleteCriticalSection(mutex)
    
#else
    typedef pthread_t rf_thread_t;
    typedef pthread_mutex_t rf_mutex_t;
    typedef pthread_cond_t rf_event_t;
    
    #define RF_THREAD_CREATE(thread, func, arg) \
        (pthread_create(thread, NULL, func, arg) == 0)
    
    #define RF_MUTEX_INIT(mutex) pthread_mutex_init(mutex, NULL)
    #define RF_MUTEX_LOCK(mutex) pthread_mutex_lock(mutex)
    #define RF_MUTEX_UNLOCK(mutex) pthread_mutex_unlock(mutex)
    #define RF_MUTEX_DESTROY(mutex) pthread_mutex_destroy(mutex)
#endif

/* ========================================================================
 * Function Declarations
 * ======================================================================== */

#ifdef __cplusplus
extern "C" {
#endif

/* Logging functions */
RF_API void rf_log_message(rf_log_level_t level, const char* file, int line, 
                          const char* format, ...);
RF_API void rf_log_init(const char* log_file);
RF_API void rf_log_cleanup(void);

/* Memory management functions */
RF_API void* rf_safe_malloc(size_t size, const char* file, int line);
RF_API void* rf_safe_calloc(size_t count, size_t size, const char* file, int line);
RF_API void* rf_safe_realloc(void* ptr, size_t size, const char* file, int line);
RF_API void rf_safe_free(void** ptr);

/* Utility functions */
RF_API rf_timestamp_t rf_get_timestamp(void);
RF_API rf_duration_t rf_get_tick_count(void);
RF_API void rf_sleep(rf_duration_t milliseconds);

/* String utilities */
RF_API char* rf_string_duplicate(const char* str);
RF_API void rf_string_trim(char* str);
RF_API rf_bool_t rf_string_equals_ignore_case(const char* str1, const char* str2);

#ifdef __cplusplus
}
#endif

#endif /* RF_COMMON_H */
