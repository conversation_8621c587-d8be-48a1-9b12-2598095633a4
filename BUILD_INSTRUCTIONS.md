# Build Instructions for RF Online Server

This document provides detailed instructions for building the refactored RF Online server using Visual Studio 2022.

## Prerequisites

### Required Software
- **Visual Studio 2022** (Community, Professional, or Enterprise)
  - Workload: "Desktop development with C++"
  - Individual components:
    - MSVC v143 - VS 2022 C++ x64/x86 build tools
    - Windows 10/11 SDK (latest version)
    - CMake tools for Visual Studio (optional)

### System Requirements
- **Operating System**: Windows 10 version 1909 or later, Windows 11
- **Architecture**: x64 (64-bit)
- **RAM**: Minimum 4 GB, recommended 8 GB or more
- **Disk Space**: At least 2 GB free space for build outputs

## Quick Start

### 1. Clone or Download the Project
```bash
git clone <repository-url>
cd RFOnlineServer
```

### 2. Open in Visual Studio 2022
1. Launch Visual Studio 2022
2. Click "Open a project or solution"
3. Navigate to the project directory
4. Select `RFOnlineServer.vcxproj`
5. Click "Open"

### 3. Build the Project
1. Select your desired configuration:
   - **Debug**: For development and debugging
   - **Release**: For production deployment
2. Select platform: **x64**
3. Build → Build Solution (Ctrl+Shift+B)

### 4. Run the Server
- Debug build: `bin\x64\Debug\RFOnlineServer_Debug.exe`
- Release build: `bin\x64\Release\RFOnlineServer.exe`

## Detailed Build Process

### Project Configuration

The project is configured with the following settings:

#### Debug Configuration
- **Optimization**: Disabled
- **Runtime Library**: Multi-threaded Debug DLL (/MDd)
- **Debug Information**: Program Database (/Zi)
- **Runtime Checks**: Enabled
- **Preprocessor Defines**: `_DEBUG`, `_CONSOLE`, `WIN32_LEAN_AND_MEAN`

#### Release Configuration
- **Optimization**: Maximum Speed (/O2)
- **Runtime Library**: Multi-threaded DLL (/MD)
- **Whole Program Optimization**: Enabled
- **Link Time Code Generation**: Enabled
- **Preprocessor Defines**: `NDEBUG`, `_CONSOLE`, `WIN32_LEAN_AND_MEAN`

### Include Directories
The project automatically includes:
- `$(ProjectDir)include` - Project header files
- Standard system include directories

### Library Dependencies
Automatically linked libraries:
- `ws2_32.lib` - Winsock 2 for networking
- `kernel32.lib` - Windows kernel functions
- `user32.lib` - Windows user interface
- `advapi32.lib` - Advanced Windows API
- Standard C runtime libraries

### Output Directories
- **Debug**: `bin\x64\Debug\`
- **Release**: `bin\x64\Release\`
- **Intermediate**: `obj\x64\Debug\` or `obj\x64\Release\`

## Build Configurations

### Debug Build
Use for development and debugging:
```
Configuration: Debug
Platform: x64
```

Features:
- Full debug information
- Runtime error checking
- Memory leak detection
- Detailed logging enabled
- No optimization for easier debugging

### Release Build
Use for production deployment:
```
Configuration: Release
Platform: x64
```

Features:
- Maximum optimization
- Minimal debug information
- Link-time code generation
- Optimized for performance
- Reduced binary size

## Troubleshooting

### Common Build Errors

#### Error: "Cannot open include file"
**Solution**: Ensure all header files are in the `include/` directory and the include path is correctly set.

#### Error: "Unresolved external symbol"
**Solution**: 
1. Check that all required libraries are linked
2. Verify function implementations exist in source files
3. Ensure proper function declarations in headers

#### Error: "LNK2019: unresolved external symbol"
**Solution**: 
1. Add missing source files to the project
2. Implement missing functions
3. Check library dependencies

#### Error: "C4996: deprecated function"
**Solution**: The project disables these warnings, but you can:
1. Use secure alternatives (e.g., `strcpy_s` instead of `strcpy`)
2. Add `#pragma warning(disable: 4996)` if needed

### Build Performance

#### Slow Build Times
**Solutions**:
1. Enable multi-processor compilation (already enabled)
2. Use SSD storage for faster I/O
3. Increase available RAM
4. Close unnecessary applications

#### Large Binary Size
**Solutions**:
1. Use Release configuration
2. Enable link-time optimization (already enabled)
3. Remove unused code and dependencies

## Advanced Build Options

### Custom Preprocessor Definitions
Add custom defines in Project Properties → C/C++ → Preprocessor:
- `RF_CUSTOM_CONFIG` - Enable custom configuration
- `RF_DEBUG_NETWORK` - Enable network debugging
- `RF_PROFILE_PERFORMANCE` - Enable performance profiling

### Additional Include Directories
If you have external dependencies:
1. Right-click project → Properties
2. C/C++ → General → Additional Include Directories
3. Add your custom include paths

### Additional Library Directories
For external libraries:
1. Project Properties → Linker → General
2. Additional Library Directories
3. Add your library paths

### Custom Build Events
Configure pre/post-build events:
1. Project Properties → Build Events
2. Add custom commands for:
   - Copying configuration files
   - Running tests
   - Deploying binaries

## Testing the Build

### Basic Functionality Test
1. Run the executable
2. Check console output for initialization messages
3. Verify log file creation in `logs/` directory
4. Test network connectivity (if applicable)

### Debug Testing
1. Set breakpoints in Visual Studio
2. Start debugging (F5)
3. Step through code execution
4. Verify variable values and program flow

### Release Testing
1. Build Release configuration
2. Test performance and stability
3. Verify all features work correctly
4. Check for memory leaks (use diagnostic tools)

## Deployment

### Debug Deployment
Include these files:
- `RFOnlineServer_Debug.exe`
- `config/server.cfg`
- Required DLLs (if any)
- Debug symbols (.pdb files) for crash analysis

### Release Deployment
Include these files:
- `RFOnlineServer.exe`
- `config/server.cfg`
- Required DLLs (if any)
- Documentation files

### Directory Structure for Deployment
```
RFOnlineServer/
├── RFOnlineServer.exe
├── config/
│   └── server.cfg
├── logs/
├── data/
└── docs/
```

## Continuous Integration

### Automated Builds
For CI/CD pipelines, use MSBuild:
```cmd
msbuild RFOnlineServer.vcxproj /p:Configuration=Release /p:Platform=x64
```

### Build Scripts
Create batch files for automated building:
```batch
@echo off
echo Building RF Online Server...
msbuild RFOnlineServer.vcxproj /p:Configuration=Release /p:Platform=x64 /m
if %ERRORLEVEL% EQU 0 (
    echo Build successful!
) else (
    echo Build failed!
    exit /b 1
)
```

## Support

### Getting Help
1. Check this documentation first
2. Review error messages carefully
3. Check Visual Studio output window
4. Consult project documentation in `docs/`

### Reporting Issues
When reporting build issues, include:
1. Visual Studio version
2. Windows version
3. Complete error messages
4. Build configuration used
5. Steps to reproduce

---

**Note**: This project requires Visual Studio 2022 with the v143 toolset. Earlier versions of Visual Studio are not supported.
