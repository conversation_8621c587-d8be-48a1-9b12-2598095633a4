/**
 * @file authentication.h
 * @brief RF Online Authentication System
 * <AUTHOR> from IDA Pro decompilation
 * @date 2025
 * 
 * This header defines the authentication system for RF Online server,
 * including user login, session management, and billing integration.
 */

#ifndef RF_AUTHENTICATION_H
#define RF_AUTHENTICATION_H

#include "rf_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * Authentication Constants
 * ======================================================================== */

#define RF_AUTH_MAX_USERNAME_LENGTH     32
#define RF_AUTH_MAX_PASSWORD_LENGTH     64
#define RF_AUTH_MAX_SESSION_TOKEN       128
#define RF_AUTH_SESSION_TIMEOUT         3600000  /* 1 hour in milliseconds */
#define RF_AUTH_MAX_LOGIN_ATTEMPTS      5
#define RF_AUTH_LOCKOUT_DURATION        300000   /* 5 minutes in milliseconds */

/* ========================================================================
 * Authentication Enumerations
 * ======================================================================== */

/* Authentication result codes */
typedef enum {
    RF_AUTH_SUCCESS = 0,
    RF_AUTH_ERROR_INVALID_CREDENTIALS = 1,
    RF_AUTH_ERROR_ACCOUNT_LOCKED = 2,
    RF_AUTH_ERROR_ACCOUNT_BANNED = 3,
    RF_AUTH_ERROR_SERVER_FULL = 4,
    RF_AUTH_ERROR_MAINTENANCE = 5,
    RF_AUTH_ERROR_VERSION_MISMATCH = 6,
    RF_AUTH_ERROR_BILLING_EXPIRED = 7,
    RF_AUTH_ERROR_ALREADY_LOGGED_IN = 8,
    RF_AUTH_ERROR_DATABASE = 9,
    RF_AUTH_ERROR_NETWORK = 10
} rf_auth_result_t;

/* Account status flags */
typedef enum {
    RF_ACCOUNT_STATUS_ACTIVE = 0,
    RF_ACCOUNT_STATUS_SUSPENDED = 1,
    RF_ACCOUNT_STATUS_BANNED = 2,
    RF_ACCOUNT_STATUS_PENDING = 3,
    RF_ACCOUNT_STATUS_EXPIRED = 4
} rf_account_status_t;

/* User privilege levels */
typedef enum {
    RF_USER_LEVEL_PLAYER = 0,
    RF_USER_LEVEL_VIP = 1,
    RF_USER_LEVEL_MODERATOR = 2,
    RF_USER_LEVEL_ADMIN = 3,
    RF_USER_LEVEL_DEVELOPER = 4
} rf_user_level_t;

/* ========================================================================
 * Authentication Structures
 * ======================================================================== */

/* User account information */
typedef struct rf_user_account {
    rf_db_id_t account_id;                              /* Unique account identifier */
    char username[RF_AUTH_MAX_USERNAME_LENGTH];         /* Account username */
    char password_hash[RF_AUTH_MAX_PASSWORD_LENGTH];    /* Hashed password */
    rf_account_status_t status;                         /* Account status */
    rf_user_level_t privilege_level;                    /* User privilege level */
    rf_timestamp_t created_time;                        /* Account creation timestamp */
    rf_timestamp_t last_login_time;                     /* Last successful login */
    rf_timestamp_t billing_expiry;                      /* Billing subscription expiry */
    rf_dword_t login_attempts;                          /* Failed login attempt count */
    rf_timestamp_t lockout_time;                        /* Account lockout timestamp */
    char email[128];                                    /* Account email address */
    char last_ip[16];                                   /* Last login IP address */
} rf_user_account_t;

/* Active session information */
typedef struct rf_user_session {
    rf_session_id_t session_id;                         /* Unique session identifier */
    rf_db_id_t account_id;                              /* Associated account ID */
    char session_token[RF_AUTH_MAX_SESSION_TOKEN];      /* Session authentication token */
    rf_timestamp_t login_time;                          /* Session start timestamp */
    rf_timestamp_t last_activity;                       /* Last activity timestamp */
    rf_socket_t client_socket;                          /* Client connection socket */
    char client_ip[16];                                 /* Client IP address */
    rf_bool_t is_authenticated;                         /* Authentication status */
    rf_user_level_t privilege_level;                    /* Cached privilege level */
} rf_user_session_t;

/* Login request structure */
typedef struct rf_login_request {
    char username[RF_AUTH_MAX_USERNAME_LENGTH];         /* Username */
    char password[RF_AUTH_MAX_PASSWORD_LENGTH];         /* Password (plaintext) */
    char client_version[32];                            /* Client version string */
    char client_ip[16];                                 /* Client IP address */
    rf_dword_t client_build;                            /* Client build number */
} rf_login_request_t;

/* Login response structure */
typedef struct rf_login_response {
    rf_auth_result_t result;                            /* Authentication result */
    rf_session_id_t session_id;                         /* Assigned session ID */
    char session_token[RF_AUTH_MAX_SESSION_TOKEN];      /* Session token */
    rf_user_level_t privilege_level;                    /* User privilege level */
    rf_timestamp_t billing_expiry;                      /* Billing expiry time */
    char message[256];                                  /* Result message */
} rf_login_response_t;

/* Billing manager structure */
typedef struct rf_billing_manager {
    rf_bool_t is_initialized;                           /* Initialization status */
    rf_mutex_t billing_mutex;                           /* Thread synchronization */
    rf_timestamp_t last_billing_check;                  /* Last billing verification */
    rf_dword_t billing_check_interval;                  /* Billing check frequency */
} rf_billing_manager_t;

/* ========================================================================
 * Authentication Manager Structure
 * ======================================================================== */

typedef struct rf_auth_manager {
    rf_bool_t is_initialized;                           /* Manager initialization status */
    rf_mutex_t auth_mutex;                              /* Thread synchronization */
    rf_user_session_t* active_sessions;                 /* Array of active sessions */
    rf_dword_t max_sessions;                            /* Maximum concurrent sessions */
    rf_dword_t current_session_count;                   /* Current active session count */
    rf_billing_manager_t billing_manager;               /* Billing system integration */
    rf_bool_t maintenance_mode;                         /* Server maintenance flag */
    char maintenance_message[256];                      /* Maintenance mode message */
} rf_auth_manager_t;

/* ========================================================================
 * Function Declarations
 * ======================================================================== */

/* Authentication Manager Functions */
RF_API rf_result_t rf_auth_manager_init(rf_auth_manager_t* manager, rf_dword_t max_sessions);
RF_API void rf_auth_manager_cleanup(rf_auth_manager_t* manager);
RF_API rf_result_t rf_auth_manager_set_maintenance(rf_auth_manager_t* manager, 
                                                   rf_bool_t enabled, const char* message);

/* User Authentication Functions */
RF_API rf_auth_result_t rf_auth_login_user(rf_auth_manager_t* manager, 
                                          const rf_login_request_t* request,
                                          rf_login_response_t* response);
RF_API rf_result_t rf_auth_logout_user(rf_auth_manager_t* manager, rf_session_id_t session_id);
RF_API rf_result_t rf_auth_validate_session(rf_auth_manager_t* manager, 
                                           rf_session_id_t session_id,
                                           const char* session_token);

/* Session Management Functions */
RF_API rf_user_session_t* rf_auth_get_session(rf_auth_manager_t* manager, 
                                              rf_session_id_t session_id);
RF_API rf_result_t rf_auth_update_session_activity(rf_auth_manager_t* manager, 
                                                   rf_session_id_t session_id);
RF_API rf_result_t rf_auth_cleanup_expired_sessions(rf_auth_manager_t* manager);

/* Account Management Functions */
RF_API rf_result_t rf_auth_load_account(const char* username, rf_user_account_t* account);
RF_API rf_result_t rf_auth_save_account(const rf_user_account_t* account);
RF_API rf_result_t rf_auth_verify_password(const char* password, const char* password_hash);
RF_API rf_result_t rf_auth_hash_password(const char* password, char* password_hash, 
                                        size_t hash_buffer_size);

/* Billing Integration Functions */
RF_API rf_result_t rf_billing_manager_init(rf_billing_manager_t* manager);
RF_API void rf_billing_manager_cleanup(rf_billing_manager_t* manager);
RF_API rf_result_t rf_billing_check_account(rf_billing_manager_t* manager, 
                                           rf_db_id_t account_id, rf_bool_t* is_valid);
RF_API rf_result_t rf_billing_update_expiry(rf_billing_manager_t* manager, 
                                           rf_db_id_t account_id, rf_timestamp_t new_expiry);

/* Security Functions */
RF_API rf_result_t rf_auth_check_ip_ban(const char* ip_address, rf_bool_t* is_banned);
RF_API rf_result_t rf_auth_add_ip_ban(const char* ip_address, rf_duration_t duration);
RF_API rf_result_t rf_auth_remove_ip_ban(const char* ip_address);

/* Utility Functions */
RF_API rf_session_id_t rf_auth_generate_session_id(void);
RF_API rf_result_t rf_auth_generate_session_token(char* token_buffer, size_t buffer_size);
RF_API rf_bool_t rf_auth_is_valid_username(const char* username);
RF_API rf_bool_t rf_auth_is_valid_password(const char* password);

#ifdef __cplusplus
}
#endif

#endif /* RF_AUTHENTICATION_H */
