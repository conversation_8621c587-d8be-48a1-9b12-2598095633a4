<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{B12702AD-ABFB-343A-A199-8E24837244A3}</ProjectGuid>
    <RootNamespace>RFOnlineServer</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>RF Online Server</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
    <EnableManagedIncrementalBuild>false</EnableManagedIncrementalBuild>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <!-- Debug Configuration Properties -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>$(ProjectName)_Debug</TargetName>
    <IncludePath>$(ProjectDir)include;$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)lib\$(Platform)\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <!-- Release Configuration Properties -->
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(SolutionDir)obj\$(Platform)\$(Configuration)\</IntDir>
    <TargetName>$(ProjectName)</TargetName>
    <IncludePath>$(ProjectDir)include;$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)lib\$(Platform)\$(Configuration);$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <!-- Debug Compiler Settings -->
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;WIN32_LEAN_AND_MEAN;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(ProjectDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <Optimization>Disabled</Optimization>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <CompileAs>CompileAsC</CompileAs>
      <DisableSpecificWarnings>4996;4100;4101;4189</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdc17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(ProjectDir)lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <!-- Release Compiler Settings -->
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;WIN32_LEAN_AND_MEAN;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>$(ProjectDir)include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <CompileAs>CompileAsC</CompileAs>
      <DisableSpecificWarnings>4996;4100;4101;4189</DisableSpecificWarnings>
      <TreatWarningAsError>false</TreatWarningAsError>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdc17</LanguageStandard>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(ProjectDir)lib\$(Platform)\$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
    </Link>
  </ItemDefinitionGroup>
  <!-- Header Files -->
  <ItemGroup Label="Header Files">
    <ClInclude Include="include\rf_common.h" />
    <ClInclude Include="include\rf_types.h" />
    <ClInclude Include="include\authentication.h" />
    <ClInclude Include="include\player.h" />
    <ClInclude Include="include\network.h" />
    <ClInclude Include="include\combat.h" />
    <ClInclude Include="include\database.h" />
    <ClInclude Include="include\economy.h" />
    <ClInclude Include="include\item.h" />
    <ClInclude Include="include\security.h" />
    <ClInclude Include="include\system.h" />
    <ClInclude Include="include\world.h" />
  </ItemGroup>
  <!-- Source Files -->
  <ItemGroup Label="Source Files">
    <!-- Core System -->
    <ClCompile Include="src\main.c" />
    <ClCompile Include="src\rf_common.c" />
    <!-- Network Module -->
    <ClCompile Include="src\network\net_socket_connect.c" />
    <ClCompile Include="src\network\network_server.c" />
    <ClCompile Include="src\network\packet_handler.c" />
    <!-- Authentication Module -->
    <ClCompile Include="src\authentication\auth_manager.c" />
    <ClCompile Include="src\authentication\billing_manager.c" />
    <ClCompile Include="src\authentication\session_manager.c" />
    <!-- Player Module -->
    <ClCompile Include="src\player\player_manager.c" />
    <ClCompile Include="src\player\character_data.c" />
    <ClCompile Include="src\player\inventory_manager.c" />
    <!-- Combat Module -->
    <ClCompile Include="src\combat\combat_system.c" />
    <ClCompile Include="src\combat\skill_system.c" />
    <ClCompile Include="src\combat\damage_calculator.c" />
    <!-- Database Module -->
    <ClCompile Include="src\database\database_manager.c" />
    <ClCompile Include="src\database\query_builder.c" />
    <ClCompile Include="src\database\connection_pool.c" />
    <!-- Economy Module -->
    <ClCompile Include="src\economy\trade_system.c" />
    <ClCompile Include="src\economy\auction_house.c" />
    <ClCompile Include="src\economy\currency_manager.c" />
    <!-- Item Module -->
    <ClCompile Include="src\item\item_manager.c" />
    <ClCompile Include="src\item\item_factory.c" />
    <ClCompile Include="src\item\enhancement_system.c" />
    <!-- Security Module -->
    <ClCompile Include="src\security\anti_cheat.c" />
    <ClCompile Include="src\security\encryption.c" />
    <ClCompile Include="src\security\access_control.c" />
    <!-- System Module -->
    <ClCompile Include="src\system\server_core.c" />
    <ClCompile Include="src\system\configuration.c" />
    <ClCompile Include="src\system\logging.c" />
    <!-- World Module -->
    <ClCompile Include="src\world\zone_manager.c" />
    <ClCompile Include="src\world\npc_manager.c" />
    <ClCompile Include="src\world\spawn_system.c" />
  </ItemGroup>
  <!-- Resource Files -->
  <ItemGroup Label="Resource Files">
    <None Include="README.md" />
    <None Include="CHANGELOG.md" />
    <None Include="LICENSE" />
    <Text Include="docs\REFACTORING_NOTES.txt" />
    <Text Include="config\server.cfg" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>