/**
 * @file player.h
 * @brief RF Online Player Management System
 * <AUTHOR> from IDA Pro decompilation
 * @date 2025
 * 
 * This header defines the player management system for RF Online server,
 * including character data, stats, inventory, and player operations.
 */

#ifndef RF_PLAYER_H
#define RF_PLAYER_H

#include "rf_common.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * Player Constants
 * ======================================================================== */

#define RF_PLAYER_MAX_NAME_LENGTH       32
#define RF_PLAYER_MAX_CHARACTERS        4
#define RF_PLAYER_MAX_INVENTORY_SLOTS   200
#define RF_PLAYER_MAX_EQUIPMENT_SLOTS   20
#define RF_PLAYER_MAX_SKILLS            100
#define RF_PLAYER_MAX_HOTKEYS           48
#define RF_PLAYER_MAX_FRIENDS           50
#define RF_PLAYER_MAX_GUILD_NAME        64

/* ========================================================================
 * Player Enumerations
 * ======================================================================== */

/* Character races */
typedef enum {
    RF_RACE_BELLATO = 0,
    RF_RACE_CORA = 1,
    RF_RACE_ACCRETIA = 2,
    RF_RACE_COUNT = 3
} rf_player_race_t;

/* Character classes */
typedef enum {
    RF_CLASS_WARRIOR = 0,
    RF_CLASS_RANGER = 1,
    RF_CLASS_SPIRITUALIST = 2,
    RF_CLASS_SPECIALIST = 3,
    RF_CLASS_COUNT = 4
} rf_player_class_t;

/* Player states */
typedef enum {
    RF_PLAYER_STATE_OFFLINE = 0,
    RF_PLAYER_STATE_ONLINE = 1,
    RF_PLAYER_STATE_COMBAT = 2,
    RF_PLAYER_STATE_DEAD = 3,
    RF_PLAYER_STATE_TRADING = 4,
    RF_PLAYER_STATE_AFK = 5
} rf_player_state_t;

/* Equipment slots */
typedef enum {
    RF_EQUIP_SLOT_HELMET = 0,
    RF_EQUIP_SLOT_ARMOR = 1,
    RF_EQUIP_SLOT_GLOVES = 2,
    RF_EQUIP_SLOT_BOOTS = 3,
    RF_EQUIP_SLOT_WEAPON_PRIMARY = 4,
    RF_EQUIP_SLOT_WEAPON_SECONDARY = 5,
    RF_EQUIP_SLOT_SHIELD = 6,
    RF_EQUIP_SLOT_ACCESSORY_1 = 7,
    RF_EQUIP_SLOT_ACCESSORY_2 = 8,
    RF_EQUIP_SLOT_RING_1 = 9,
    RF_EQUIP_SLOT_RING_2 = 10,
    RF_EQUIP_SLOT_NECKLACE = 11,
    RF_EQUIP_SLOT_BELT = 12,
    RF_EQUIP_SLOT_BACKPACK = 13,
    RF_EQUIP_SLOT_COUNT = 14
} rf_equipment_slot_t;

/* ========================================================================
 * Player Structures
 * ======================================================================== */

/* Character statistics */
typedef struct rf_player_stats {
    rf_level_t level;                                   /* Character level */
    rf_experience_t experience;                         /* Current experience */
    rf_experience_t experience_next;                    /* Experience needed for next level */
    
    /* Primary stats */
    rf_dword_t strength;                                /* Physical power */
    rf_dword_t dexterity;                               /* Agility and accuracy */
    rf_dword_t intelligence;                            /* Magical power */
    rf_dword_t constitution;                            /* Health and defense */
    
    /* Secondary stats */
    rf_dword_t health_current;                          /* Current health points */
    rf_dword_t health_max;                              /* Maximum health points */
    rf_dword_t mana_current;                            /* Current mana points */
    rf_dword_t mana_max;                                /* Maximum mana points */
    rf_dword_t stamina_current;                         /* Current stamina */
    rf_dword_t stamina_max;                             /* Maximum stamina */
    
    /* Combat stats */
    rf_dword_t attack_power;                            /* Physical attack power */
    rf_dword_t magic_power;                             /* Magical attack power */
    rf_dword_t defense_physical;                        /* Physical defense */
    rf_dword_t defense_magical;                         /* Magical defense */
    rf_dword_t accuracy;                                /* Hit chance */
    rf_dword_t dodge;                                   /* Dodge chance */
    rf_dword_t critical_rate;                           /* Critical hit rate */
    
    /* Available stat points */
    rf_dword_t stat_points;                             /* Unallocated stat points */
    rf_dword_t skill_points;                            /* Unallocated skill points */
} rf_player_stats_t;

/* Player position and movement */
typedef struct rf_player_position {
    rf_position_t coordinates;                          /* 3D world coordinates */
    rf_coord_t rotation;                                /* Character rotation */
    rf_dword_t zone_id;                                 /* Current zone identifier */
    rf_dword_t map_id;                                  /* Current map identifier */
    rf_bool_t is_moving;                                /* Movement state */
    rf_coord_t movement_speed;                          /* Movement speed */
} rf_player_position_t;

/* Inventory item structure */
typedef struct rf_inventory_item {
    rf_item_id_t item_id;                               /* Item identifier */
    rf_dword_t quantity;                                /* Item quantity */
    rf_dword_t durability;                              /* Item durability */
    rf_dword_t enhancement_level;                       /* Enhancement level */
    rf_qword_t item_flags;                              /* Item property flags */
    rf_timestamp_t expiry_time;                         /* Item expiry timestamp */
} rf_inventory_item_t;

/* Player skill information */
typedef struct rf_player_skill {
    rf_dword_t skill_id;                                /* Skill identifier */
    rf_level_t skill_level;                             /* Current skill level */
    rf_experience_t skill_experience;                   /* Skill experience */
    rf_timestamp_t cooldown_end;                        /* Skill cooldown end time */
    rf_bool_t is_learned;                               /* Skill learned status */
} rf_player_skill_t;

/* Player hotkey configuration */
typedef struct rf_player_hotkey {
    rf_dword_t slot_index;                              /* Hotkey slot index */
    rf_dword_t action_type;                             /* Action type (skill/item/etc) */
    rf_dword_t action_id;                               /* Action identifier */
    rf_bool_t is_active;                                /* Hotkey active status */
} rf_player_hotkey_t;

/* Main character structure */
typedef struct rf_player_character {
    rf_player_id_t character_id;                        /* Unique character identifier */
    rf_db_id_t account_id;                              /* Associated account ID */
    char character_name[RF_PLAYER_MAX_NAME_LENGTH];     /* Character name */
    
    /* Character properties */
    rf_player_race_t race;                              /* Character race */
    rf_player_class_t character_class;                  /* Character class */
    rf_player_state_t state;                            /* Current player state */
    rf_player_stats_t stats;                            /* Character statistics */
    rf_player_position_t position;                      /* Position and movement */
    
    /* Inventory and equipment */
    rf_inventory_item_t inventory[RF_PLAYER_MAX_INVENTORY_SLOTS];  /* Inventory items */
    rf_inventory_item_t equipment[RF_PLAYER_MAX_EQUIPMENT_SLOTS];  /* Equipped items */
    rf_money_t money;                                   /* Character currency */
    
    /* Skills and abilities */
    rf_player_skill_t skills[RF_PLAYER_MAX_SKILLS];     /* Character skills */
    rf_player_hotkey_t hotkeys[RF_PLAYER_MAX_HOTKEYS];  /* Hotkey configuration */
    
    /* Social information */
    char guild_name[RF_PLAYER_MAX_GUILD_NAME];          /* Guild membership */
    rf_dword_t guild_rank;                              /* Guild rank */
    rf_player_id_t friends[RF_PLAYER_MAX_FRIENDS];      /* Friend list */
    
    /* Timestamps */
    rf_timestamp_t created_time;                        /* Character creation time */
    rf_timestamp_t last_login;                          /* Last login timestamp */
    rf_timestamp_t last_logout;                         /* Last logout timestamp */
    rf_duration_t play_time;                            /* Total play time */
    
    /* Runtime data */
    rf_session_id_t session_id;                         /* Associated session ID */
    rf_socket_t client_socket;                          /* Client connection */
    rf_bool_t is_online;                                /* Online status */
    rf_timestamp_t last_activity;                       /* Last activity timestamp */
} rf_player_character_t;

/* Player manager structure */
typedef struct rf_player_manager {
    rf_bool_t is_initialized;                           /* Manager initialization status */
    rf_mutex_t player_mutex;                            /* Thread synchronization */
    rf_player_character_t* online_players;              /* Array of online players */
    rf_dword_t max_players;                             /* Maximum concurrent players */
    rf_dword_t current_player_count;                    /* Current online player count */
    rf_bool_t auto_save_enabled;                        /* Auto-save feature flag */
    rf_duration_t auto_save_interval;                   /* Auto-save frequency */
    rf_timestamp_t last_auto_save;                      /* Last auto-save timestamp */
} rf_player_manager_t;

/* ========================================================================
 * Function Declarations
 * ======================================================================== */

/* Player Manager Functions */
RF_API rf_result_t rf_player_manager_init(rf_player_manager_t* manager, rf_dword_t max_players);
RF_API void rf_player_manager_cleanup(rf_player_manager_t* manager);
RF_API rf_result_t rf_player_manager_update(rf_player_manager_t* manager);

/* Character Management Functions */
RF_API rf_result_t rf_player_create_character(rf_player_manager_t* manager,
                                             rf_db_id_t account_id,
                                             const char* character_name,
                                             rf_player_race_t race,
                                             rf_player_class_t character_class,
                                             rf_player_character_t* character);
RF_API rf_result_t rf_player_delete_character(rf_player_manager_t* manager,
                                             rf_player_id_t character_id);
RF_API rf_result_t rf_player_load_character(rf_player_id_t character_id,
                                           rf_player_character_t* character);
RF_API rf_result_t rf_player_save_character(const rf_player_character_t* character);

/* Player Session Functions */
RF_API rf_result_t rf_player_login(rf_player_manager_t* manager,
                                  rf_player_id_t character_id,
                                  rf_session_id_t session_id,
                                  rf_socket_t client_socket);
RF_API rf_result_t rf_player_logout(rf_player_manager_t* manager,
                                   rf_player_id_t character_id);
RF_API rf_player_character_t* rf_player_get_by_id(rf_player_manager_t* manager,
                                                  rf_player_id_t character_id);
RF_API rf_player_character_t* rf_player_get_by_name(rf_player_manager_t* manager,
                                                    const char* character_name);

#ifdef __cplusplus
}
#endif

#endif /* RF_PLAYER_H */
