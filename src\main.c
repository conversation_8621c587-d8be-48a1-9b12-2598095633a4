/**
 * @file main.c
 * @brief RF Online Server Main Entry Point
 * <AUTHOR> from IDA Pro decompilation
 * @date 2025
 * 
 * This file contains the main entry point for the RF Online server application.
 * It initializes all subsystems and manages the main server loop.
 */

#include "rf_common.h"
#include "authentication.h"
#include "player.h"
#include "network.h"

/* ========================================================================
 * Global Variables
 * ======================================================================== */

static rf_bool_t g_server_running = false;
static rf_network_server_t g_network_server;
static rf_auth_manager_t g_auth_manager;
static rf_player_manager_t g_player_manager;

/* ========================================================================
 * Function Declarations
 * ======================================================================== */

/**
 * @brief Initialize all server subsystems
 * @return RF_SUCCESS on success, error code on failure
 */
static rf_result_t rf_server_initialize(void);

/**
 * @brief Cleanup all server subsystems
 */
static void rf_server_cleanup(void);

/**
 * @brief Main server update loop
 * @return RF_SUCCESS on success, error code on failure
 */
static rf_result_t rf_server_update(void);

/**
 * @brief Signal handler for graceful shutdown
 * @param signal Signal number
 */
static void rf_signal_handler(int signal);

/* ========================================================================
 * Main Entry Point
 * ======================================================================== */

/**
 * @brief Main entry point for RF Online Server
 * @param argc Argument count
 * @param argv Argument vector
 * @return 0 on success, non-zero on error
 */
int main(int argc, char* argv[])
{
    RF_INFO("RF Online Server v%d.%d.%d starting...", 
           RF_SERVER_VERSION_MAJOR, 
           RF_SERVER_VERSION_MINOR, 
           RF_SERVER_VERSION_PATCH);
    
    /* Initialize logging system */
    rf_log_init("server.log");
    
    /* Set up signal handlers for graceful shutdown */
#ifdef _WIN32
    signal(SIGINT, rf_signal_handler);
    signal(SIGTERM, rf_signal_handler);
#else
    signal(SIGINT, rf_signal_handler);
    signal(SIGTERM, rf_signal_handler);
    signal(SIGPIPE, SIG_IGN); /* Ignore broken pipe signals */
#endif
    
    /* Initialize server subsystems */
    rf_result_t init_result = rf_server_initialize();
    if (init_result != RF_SUCCESS) {
        RF_FATAL("Failed to initialize server subsystems: %d", init_result);
        rf_server_cleanup();
        return EXIT_FAILURE;
    }
    
    RF_INFO("Server initialization complete. Server is now running.");
    g_server_running = true;
    
    /* Main server loop */
    while (g_server_running) {
        rf_result_t update_result = rf_server_update();
        if (update_result != RF_SUCCESS) {
            RF_ERROR("Server update failed: %d", update_result);
            break;
        }
        
        /* Small delay to prevent excessive CPU usage */
        rf_sleep(10);
    }
    
    RF_INFO("Server shutting down...");
    rf_server_cleanup();
    rf_log_cleanup();
    
    RF_INFO("Server shutdown complete.");
    return EXIT_SUCCESS;
}

/* ========================================================================
 * Function Implementations
 * ======================================================================== */

/**
 * @brief Initialize all server subsystems
 */
static rf_result_t rf_server_initialize(void)
{
    rf_result_t result;
    
    RF_INFO("Initializing server subsystems...");
    
    /* Initialize network subsystem */
    RF_INFO("Initializing network server...");
    result = rf_network_server_init(&g_network_server, RF_DEFAULT_PORT, RF_MAX_CLIENTS);
    if (result != RF_SUCCESS) {
        RF_ERROR("Failed to initialize network server: %d", result);
        return result;
    }
    
    /* Initialize authentication manager */
    RF_INFO("Initializing authentication manager...");
    result = rf_auth_manager_init(&g_auth_manager, RF_MAX_CLIENTS);
    if (result != RF_SUCCESS) {
        RF_ERROR("Failed to initialize authentication manager: %d", result);
        return result;
    }
    
    /* Initialize player manager */
    RF_INFO("Initializing player manager...");
    result = rf_player_manager_init(&g_player_manager, RF_MAX_CLIENTS);
    if (result != RF_SUCCESS) {
        RF_ERROR("Failed to initialize player manager: %d", result);
        return result;
    }
    
    /* Start network server */
    RF_INFO("Starting network server...");
    result = rf_network_server_start(&g_network_server);
    if (result != RF_SUCCESS) {
        RF_ERROR("Failed to start network server: %d", result);
        return result;
    }
    
    RF_INFO("All subsystems initialized successfully.");
    return RF_SUCCESS;
}

/**
 * @brief Cleanup all server subsystems
 */
static void rf_server_cleanup(void)
{
    RF_INFO("Cleaning up server subsystems...");
    
    /* Stop and cleanup network server */
    rf_network_server_stop(&g_network_server);
    rf_network_server_cleanup(&g_network_server);
    
    /* Cleanup managers */
    rf_player_manager_cleanup(&g_player_manager);
    rf_auth_manager_cleanup(&g_auth_manager);
    
    RF_INFO("Server cleanup complete.");
}

/**
 * @brief Main server update loop
 */
static rf_result_t rf_server_update(void)
{
    rf_result_t result;
    
    /* Update network server */
    result = rf_network_server_update(&g_network_server);
    if (result != RF_SUCCESS) {
        RF_ERROR("Network server update failed: %d", result);
        return result;
    }
    
    /* Update player manager */
    result = rf_player_manager_update(&g_player_manager);
    if (result != RF_SUCCESS) {
        RF_ERROR("Player manager update failed: %d", result);
        return result;
    }
    
    /* Cleanup expired sessions */
    result = rf_auth_cleanup_expired_sessions(&g_auth_manager);
    if (result != RF_SUCCESS) {
        RF_WARN("Failed to cleanup expired sessions: %d", result);
        /* Non-critical error, continue */
    }
    
    return RF_SUCCESS;
}

/**
 * @brief Signal handler for graceful shutdown
 */
static void rf_signal_handler(int signal)
{
    switch (signal) {
        case SIGINT:
            RF_INFO("Received SIGINT, initiating graceful shutdown...");
            g_server_running = false;
            break;
            
        case SIGTERM:
            RF_INFO("Received SIGTERM, initiating graceful shutdown...");
            g_server_running = false;
            break;
            
        default:
            RF_WARN("Received unknown signal: %d", signal);
            break;
    }
}
