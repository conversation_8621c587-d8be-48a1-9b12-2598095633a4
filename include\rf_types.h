/**
 * @file rf_types.h
 * @brief RF Online Common Type Definitions
 * <AUTHOR> from IDA Pro decompilation
 * @date 2025
 * 
 * This header provides modern, clean type definitions to replace
 * generic IDA Pro types with meaningful, standard C types.
 */

#ifndef RF_TYPES_H
#define RF_TYPES_H

#include <stdint.h>
#include <stdbool.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* ========================================================================
 * Standard Type Replacements for IDA Pro Generic Types
 * ======================================================================== */

/* Replace IDA Pro generic integer types with standard types */
typedef uint8_t     rf_byte_t;      /* Replaces _BYTE */
typedef uint16_t    rf_word_t;      /* Replaces _WORD */
typedef uint32_t    rf_dword_t;     /* Replaces _DWORD */
typedef uint64_t    rf_qword_t;     /* Replaces _QWORD */

/* Signed variants */
typedef int8_t      rf_sbyte_t;
typedef int16_t     rf_sword_t;
typedef int32_t     rf_sdword_t;
typedef int64_t     rf_sqword_t;

/* Pointer-sized types for 64-bit compatibility */
typedef uintptr_t   rf_ptr_t;
typedef intptr_t    rf_sptr_t;

/* ========================================================================
 * RF Online Specific Types
 * ======================================================================== */

/* Game Object Identifiers */
typedef uint32_t    rf_object_id_t;     /* Game object unique identifier */
typedef uint32_t    rf_player_id_t;     /* Player unique identifier */
typedef uint32_t    rf_item_id_t;       /* Item unique identifier */
typedef uint32_t    rf_npc_id_t;        /* NPC unique identifier */

/* Network and Communication */
typedef uint16_t    rf_packet_type_t;   /* Network packet type identifier */
typedef uint32_t    rf_session_id_t;    /* Client session identifier */
typedef uint64_t    rf_socket_t;        /* Socket handle */

/* Game Mechanics */
typedef uint32_t    rf_level_t;         /* Character/item level */
typedef uint64_t    rf_experience_t;    /* Experience points */
typedef uint64_t    rf_money_t;         /* In-game currency */
typedef uint32_t    rf_race_t;          /* Character race */
typedef uint32_t    rf_class_t;         /* Character class */

/* Coordinates and Positioning */
typedef float       rf_coord_t;         /* 3D coordinate component */
typedef struct {
    rf_coord_t x, y, z;
} rf_position_t;

/* Time and Duration */
typedef uint64_t    rf_timestamp_t;     /* Unix timestamp */
typedef uint32_t    rf_duration_t;      /* Duration in milliseconds */

/* Database and Storage */
typedef uint64_t    rf_db_id_t;         /* Database record identifier */
typedef uint32_t    rf_db_result_t;     /* Database operation result */

/* ========================================================================
 * Boolean and Status Types
 * ======================================================================== */

/* Boolean type for clear true/false semantics */
typedef bool        rf_bool_t;

/* Common result/status codes */
typedef enum {
    RF_SUCCESS = 0,
    RF_ERROR_GENERAL = -1,
    RF_ERROR_INVALID_PARAM = -2,
    RF_ERROR_OUT_OF_MEMORY = -3,
    RF_ERROR_NOT_FOUND = -4,
    RF_ERROR_ACCESS_DENIED = -5,
    RF_ERROR_TIMEOUT = -6,
    RF_ERROR_NETWORK = -7,
    RF_ERROR_DATABASE = -8
} rf_result_t;

/* ========================================================================
 * String and Buffer Types
 * ======================================================================== */

/* String length constants */
#define RF_MAX_NAME_LENGTH      64
#define RF_MAX_MESSAGE_LENGTH   512
#define RF_MAX_PATH_LENGTH      260

/* String types */
typedef char        rf_name_t[RF_MAX_NAME_LENGTH];
typedef char        rf_message_t[RF_MAX_MESSAGE_LENGTH];
typedef char        rf_path_t[RF_MAX_PATH_LENGTH];

/* ========================================================================
 * Utility Macros
 * ======================================================================== */

/* Safe array size calculation */
#define RF_ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))

/* Bit manipulation helpers */
#define RF_SET_BIT(value, bit)      ((value) |= (1U << (bit)))
#define RF_CLEAR_BIT(value, bit)    ((value) &= ~(1U << (bit)))
#define RF_TEST_BIT(value, bit)     (((value) & (1U << (bit))) != 0)

/* Alignment macros */
#define RF_ALIGN_UP(value, align)   (((value) + (align) - 1) & ~((align) - 1))
#define RF_ALIGN_DOWN(value, align) ((value) & ~((align) - 1))

/* Min/Max macros (if not already defined) */
#ifndef RF_MIN
#define RF_MIN(a, b) ((a) < (b) ? (a) : (b))
#endif

#ifndef RF_MAX
#define RF_MAX(a, b) ((a) > (b) ? (a) : (b))
#endif

/* ========================================================================
 * Compiler and Platform Compatibility
 * ======================================================================== */

/* Visual Studio 2022 compatibility */
#ifdef _MSC_VER
    #pragma warning(push)
    #pragma warning(disable: 4996) /* Disable deprecated function warnings */
    
    /* Force inline for performance-critical functions */
    #define RF_FORCE_INLINE __forceinline
    
    /* Calling conventions */
    #define RF_CDECL    __cdecl
    #define RF_STDCALL  __stdcall
    #define RF_FASTCALL __fastcall
    
#else
    #define RF_FORCE_INLINE inline
    #define RF_CDECL
    #define RF_STDCALL
    #define RF_FASTCALL
#endif

/* Export/Import macros for DLL builds */
#ifdef RF_BUILD_DLL
    #define RF_API __declspec(dllexport)
#elif defined(RF_USE_DLL)
    #define RF_API __declspec(dllimport)
#else
    #define RF_API
#endif

#ifdef __cplusplus
}
#endif

#endif /* RF_TYPES_H */
