/**
 * @file net_socket_connect.c
 * @brief Network Socket Connection Implementation
 * <AUTHOR> from IDA Pro decompilation (Original: ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_14047E830.c)
 * @date 2025
 * 
 * This file implements socket connection functionality for the RF Online server.
 * Refactored from decompiled code to use modern C conventions and meaningful names.
 * 
 * Original function: CNetSocket::Connect
 * Original address: 0x14047E830
 */

#include "network.h"
#include "rf_common.h"

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
#endif

/* ========================================================================
 * Constants and Definitions
 * ======================================================================== */

/* Protocol type constants (inferred from original code) */
#define RF_PROTOCOL_TCP         0
#define RF_PROTOCOL_UDP         1

/* Socket event flags for WSAEventSelect */
#define RF_SOCKET_EVENTS        (FD_READ | FD_WRITE | FD_CLOSE | FD_CONNECT)

/* Error codes */
#define RF_SOCKET_ERROR_ALREADY_CONNECTED   -1
#define RF_SOCKET_ERROR_CONNECTION_FAILED   -2
#define RF_SOCKET_ERROR_EVENT_SELECT        -3

/* ========================================================================
 * Internal Structures (inferred from original code)
 * ======================================================================== */

/* Socket type configuration */
typedef struct rf_socket_type_config {
    rf_byte_t protocol_id;                              /* Protocol identifier */
    /* Additional fields inferred from usage patterns */
} rf_socket_type_config_t;

/* Individual socket entry */
typedef struct rf_socket_entry {
    rf_socket_t socket_handle;                          /* System socket handle */
    rf_bool_t is_accepting;                             /* Accept state flag */
    /* Additional socket properties */
} rf_socket_entry_t;

/* Network socket manager (inferred from CNetSocket class) */
typedef struct rf_net_socket_manager {
    rf_socket_entry_t* socket_array;                    /* Array of socket entries */
    rf_dword_t socket_count;                            /* Number of sockets */
    void** event_handles;                               /* Array of event handles */
    rf_socket_type_config_t socket_type;                /* Socket type configuration */
    /* Additional manager properties */
} rf_net_socket_manager_t;

/* ========================================================================
 * Private Function Declarations
 * ======================================================================== */

/**
 * @brief Initialize local buffer with debug pattern
 * @param buffer Pointer to buffer to initialize
 * @param size_in_dwords Size of buffer in DWORD units
 * 
 * This function replicates the original buffer initialization pattern
 * found in the decompiled code (filling with -858993460 / 0xCCCCCCCC).
 */
static void rf_init_debug_buffer(rf_dword_t* buffer, size_t size_in_dwords);

/**
 * @brief Create socket based on protocol type
 * @param protocol_id Protocol identifier (0=TCP, 1=UDP)
 * @param socket_handle Output parameter for created socket
 * @param address_length Output parameter for address structure length
 * @return RF_SUCCESS on success, error code on failure
 */
static rf_result_t rf_create_protocol_socket(rf_byte_t protocol_id, 
                                            rf_socket_t* socket_handle,
                                            int* address_length);

/* ========================================================================
 * Public Function Implementation
 * ======================================================================== */

/**
 * @brief Connect a network socket to a remote address
 * @param socket_manager Pointer to the network socket manager
 * @param socket_index Index of the socket in the manager's array
 * @param remote_address Pointer to the remote socket address structure
 * @return 0 on success, negative error code on failure
 * 
 * This function establishes a connection to a remote address using the specified
 * socket from the manager's socket array. It handles both TCP and UDP protocols
 * based on the manager's configuration.
 * 
 * Original function signature:
 * int __fastcall CNetSocket::Connect(CNetSocket *this, unsigned int n, sockaddr_in *pAddr)
 */
rf_result_t rf_net_socket_connect(rf_net_socket_manager_t* socket_manager,
                                 rf_dword_t socket_index,
                                 const struct sockaddr_in* remote_address)
{
    /* Input validation */
    RF_CHECK_NULL(socket_manager);
    RF_CHECK_NULL(remote_address);
    
    if (socket_index >= socket_manager->socket_count) {
        RF_ERROR("Invalid socket index: %u (max: %u)", socket_index, socket_manager->socket_count);
        return RF_ERROR_INVALID_PARAM;
    }
    
    /* Local variables with meaningful names (replacing original v3, v7, etc.) */
    rf_dword_t debug_buffer[16];                        /* Local debug buffer (original v7) */
    int address_length;                                 /* Socket address length (original namelen) */
    rf_socket_entry_t* target_socket;                   /* Target socket entry */
    rf_socket_t new_socket_handle;                      /* New socket handle */
    
    /* Initialize debug buffer (replicates original buffer initialization) */
    rf_init_debug_buffer(debug_buffer, RF_ARRAY_SIZE(debug_buffer));
    
    /* Get target socket entry */
    target_socket = &socket_manager->socket_array[socket_index];
    
    /* Check if socket is already in accepting/connected state */
    if (target_socket->is_accepting) {
        RF_WARN("Socket %u is already in accepting state", socket_index);
        return RF_SOCKET_ERROR_ALREADY_CONNECTED;
    }
    
    /* Create socket based on protocol type */
    rf_result_t create_result = rf_create_protocol_socket(
        socket_manager->socket_type.protocol_id,
        &new_socket_handle,
        &address_length
    );
    
    if (create_result != RF_SUCCESS) {
        RF_ERROR("Failed to create socket for protocol %u", 
                socket_manager->socket_type.protocol_id);
        return create_result;
    }
    
    /* Store the new socket handle */
    target_socket->socket_handle = new_socket_handle;
    
    /* Attempt to connect to the remote address */
    int connect_result = connect(new_socket_handle, 
                                (const struct sockaddr*)remote_address, 
                                address_length);
    
    if (connect_result == SOCKET_ERROR) {
        /* Connection failed - cleanup and return error */
        RF_WARN("Connection failed to %s:%u", 
               inet_ntoa(remote_address->sin_addr), 
               ntohs(remote_address->sin_port));
        
        closesocket(new_socket_handle);
        target_socket->socket_handle = INVALID_SOCKET;
        
#ifdef _WIN32
        int wsa_error = WSAGetLastError();
        RF_ERROR("WSA Error: %d", wsa_error);
        return wsa_error;
#else
        RF_ERROR("Connect error: %s", strerror(errno));
        return RF_SOCKET_ERROR_CONNECTION_FAILED;
#endif
    }
    
    /* Connection successful - set up event monitoring (Windows only) */
#ifdef _WIN32
    if (socket_manager->event_handles && socket_manager->event_handles[socket_index]) {
        int event_result = WSAEventSelect(new_socket_handle,
                                         socket_manager->event_handles[socket_index],
                                         RF_SOCKET_EVENTS);
        
        if (event_result == SOCKET_ERROR) {
            RF_ERROR("Failed to set up socket event monitoring");
            return RF_SOCKET_ERROR_EVENT_SELECT;
        }
    }
#endif
    
    RF_INFO("Successfully connected socket %u to %s:%u", 
           socket_index,
           inet_ntoa(remote_address->sin_addr), 
           ntohs(remote_address->sin_port));
    
    return RF_SUCCESS;
}

/* ========================================================================
 * Private Function Implementations
 * ======================================================================== */

/**
 * @brief Initialize local buffer with debug pattern
 * 
 * This replicates the original code's buffer initialization pattern.
 * The original code filled a 64-byte buffer with the value -858993460 (0xCCCCCCCC),
 * which is a common debug pattern in Visual Studio debug builds.
 */
static void rf_init_debug_buffer(rf_dword_t* buffer, size_t size_in_dwords)
{
    const rf_dword_t DEBUG_PATTERN = 0xCCCCCCCC;  /* -858993460 in original code */
    
    for (size_t i = 0; i < size_in_dwords; ++i) {
        buffer[i] = DEBUG_PATTERN;
    }
}

/**
 * @brief Create socket based on protocol type
 * 
 * This function creates either a TCP or UDP socket based on the protocol ID.
 * The original code used hardcoded values that have been replaced with
 * standard socket API constants.
 */
static rf_result_t rf_create_protocol_socket(rf_byte_t protocol_id, 
                                            rf_socket_t* socket_handle,
                                            int* address_length)
{
    RF_CHECK_NULL(socket_handle);
    RF_CHECK_NULL(address_length);
    
    switch (protocol_id) {
        case RF_PROTOCOL_TCP:
            /* Original: socket(2, 1, 0) - TCP socket */
            *socket_handle = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            *address_length = sizeof(struct sockaddr_in);
            break;
            
        case RF_PROTOCOL_UDP:
            /* Original: socket(6, 5, 1256) - appears to be UDP with custom protocol */
            /* Note: Original values seem unusual, using standard UDP instead */
            *socket_handle = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
            *address_length = sizeof(struct sockaddr_in);
            RF_WARN("Using standard UDP instead of original protocol values (6, 5, 1256)");
            break;
            
        default:
            RF_ERROR("Unknown protocol ID: %u", protocol_id);
            return RF_ERROR_INVALID_PARAM;
    }
    
    if (*socket_handle == INVALID_SOCKET) {
#ifdef _WIN32
        int error = WSAGetLastError();
        RF_ERROR("Socket creation failed with WSA error: %d", error);
        return error;
#else
        RF_ERROR("Socket creation failed: %s", strerror(errno));
        return RF_ERROR_GENERAL;
#endif
    }
    
    return RF_SUCCESS;
}

/* ========================================================================
 * Additional Utility Functions
 * ======================================================================== */

/**
 * @brief Initialize a network socket manager
 * @param manager Pointer to the manager structure to initialize
 * @param max_sockets Maximum number of sockets to support
 * @return RF_SUCCESS on success, error code on failure
 */
rf_result_t rf_net_socket_manager_init(rf_net_socket_manager_t* manager, rf_dword_t max_sockets)
{
    RF_CHECK_NULL(manager);
    
    if (max_sockets == 0) {
        RF_ERROR("Invalid max_sockets value: 0");
        return RF_ERROR_INVALID_PARAM;
    }
    
    /* Initialize manager structure */
    memset(manager, 0, sizeof(rf_net_socket_manager_t));
    
    /* Allocate socket array */
    manager->socket_array = RF_CALLOC(max_sockets, sizeof(rf_socket_entry_t));
    if (!manager->socket_array) {
        RF_ERROR("Failed to allocate socket array for %u sockets", max_sockets);
        return RF_ERROR_OUT_OF_MEMORY;
    }
    
    /* Allocate event handle array (Windows only) */
#ifdef _WIN32
    manager->event_handles = RF_CALLOC(max_sockets, sizeof(void*));
    if (!manager->event_handles) {
        RF_ERROR("Failed to allocate event handle array");
        RF_FREE(manager->socket_array);
        return RF_ERROR_OUT_OF_MEMORY;
    }
#endif
    
    manager->socket_count = max_sockets;
    
    /* Initialize all socket handles to invalid */
    for (rf_dword_t i = 0; i < max_sockets; ++i) {
        manager->socket_array[i].socket_handle = INVALID_SOCKET;
        manager->socket_array[i].is_accepting = false;
    }
    
    RF_INFO("Network socket manager initialized with %u sockets", max_sockets);
    return RF_SUCCESS;
}

/**
 * @brief Cleanup a network socket manager
 * @param manager Pointer to the manager structure to cleanup
 */
void rf_net_socket_manager_cleanup(rf_net_socket_manager_t* manager)
{
    if (!manager) {
        return;
    }
    
    /* Close all open sockets */
    if (manager->socket_array) {
        for (rf_dword_t i = 0; i < manager->socket_count; ++i) {
            if (manager->socket_array[i].socket_handle != INVALID_SOCKET) {
                closesocket(manager->socket_array[i].socket_handle);
                manager->socket_array[i].socket_handle = INVALID_SOCKET;
            }
        }
        RF_FREE(manager->socket_array);
    }
    
    /* Cleanup event handles (Windows only) */
#ifdef _WIN32
    if (manager->event_handles) {
        for (rf_dword_t i = 0; i < manager->socket_count; ++i) {
            if (manager->event_handles[i]) {
                CloseHandle(manager->event_handles[i]);
                manager->event_handles[i] = NULL;
            }
        }
        RF_FREE(manager->event_handles);
    }
#endif
    
    /* Clear manager structure */
    memset(manager, 0, sizeof(rf_net_socket_manager_t));
    
    RF_INFO("Network socket manager cleaned up");
}
