# RF Online Server - Refactored Edition

A modernized and refactored RF Online server implementation based on decompiled source code, optimized for Visual Studio 2022 and modern C development practices.

## Overview

This project represents a comprehensive refactoring of RF Online server code that was originally decompiled using IDA Pro. The codebase has been restructured to follow modern C conventions, improve readability, and ensure compatibility with Visual Studio 2022.

## Features

- **Modern C Standards**: Refactored to use C17 standard with clean, readable code
- **Modular Architecture**: Organized into logical modules (authentication, player, network, etc.)
- **Visual Studio 2022 Compatible**: Full project configuration for VS2022 with v143 toolset
- **Type Safety**: Replaced generic IDA Pro types with meaningful, standard C types
- **Comprehensive Documentation**: Extensive inline documentation and comments
- **Cross-Platform Ready**: Designed with cross-platform compatibility in mind

## Project Structure

```
RFOnlineServer/
├── include/                    # Header files
│   ├── rf_common.h            # Common definitions and utilities
│   ├── rf_types.h             # Type definitions and replacements
│   ├── authentication.h       # Authentication system
│   ├── player.h               # Player management
│   ├── network.h              # Network communication
│   ├── combat.h               # Combat system
│   ├── database.h             # Database operations
│   ├── economy.h              # Economy and trading
│   ├── item.h                 # Item management
│   ├── security.h             # Security and anti-cheat
│   ├── system.h               # Core system functions
│   └── world.h                # World and zone management
├── src/                       # Source files
│   ├── main.c                 # Main entry point
│   ├── rf_common.c            # Common utility implementations
│   ├── authentication/        # Authentication module
│   ├── combat/                # Combat system
│   ├── database/              # Database operations
│   ├── economy/               # Economy system
│   ├── item/                  # Item management
│   ├── network/               # Network communication
│   ├── player/                # Player management
│   ├── security/              # Security systems
│   ├── system/                # Core system
│   └── world/                 # World management
├── config/                    # Configuration files
├── docs/                      # Documentation
├── Decompiled Source Code - IDA Pro/  # Original decompiled files
├── Decompiled Header - IDA Pro/       # Original header file
├── RFOnlineServer.vcxproj     # Visual Studio project file
└── RFOnlineServer.vcxproj.filters  # VS project filters
```

## Building the Project

### Prerequisites

- Visual Studio 2022 (Community, Professional, or Enterprise)
- Windows 10/11 SDK
- C17 compiler support

### Build Instructions

1. **Clone the Repository**
   ```bash
   git clone <repository-url>
   cd RFOnlineServer
   ```

2. **Open in Visual Studio 2022**
   - Open `RFOnlineServer.vcxproj` in Visual Studio 2022
   - The project is configured with both Debug and Release configurations

3. **Build the Project**
   - Select your desired configuration (Debug/Release)
   - Build → Build Solution (Ctrl+Shift+B)

4. **Run the Server**
   - The compiled executable will be in `bin/x64/Debug/` or `bin/x64/Release/`
   - Run `RFOnlineServer_Debug.exe` or `RFOnlineServer.exe`

## Configuration

The server can be configured through:
- `config/server.cfg` - Main server configuration
- Command line arguments
- Environment variables

## Key Refactoring Changes

### Type System Modernization

- **Before**: `_DWORD`, `_BYTE`, `_QWORD`, `__int64`
- **After**: `rf_dword_t`, `rf_byte_t`, `rf_qword_t`, `rf_sqword_t` (using standard `stdint.h` types)

### Function Naming

- **Before**: Mangled names like `ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_14047E830`
- **After**: Descriptive names like `rf_net_socket_connect`

### Code Organization

- **Before**: Single monolithic header file (82K+ lines)
- **After**: Modular headers organized by functionality

### Documentation

- **Before**: Minimal comments, unclear variable names
- **After**: Comprehensive documentation, meaningful variable names

## Module Overview

### Authentication Module
Handles user login, session management, and billing integration.

### Player Module
Manages character data, statistics, inventory, and player operations.

### Network Module
Provides socket management, packet handling, and client connections.

### Combat Module
Implements combat mechanics, skill systems, and damage calculations.

### Database Module
Manages database connections, queries, and data persistence.

### Economy Module
Handles trading, auction house, and currency management.

### Item Module
Manages item creation, enhancement, and properties.

### Security Module
Provides anti-cheat, encryption, and access control.

### System Module
Core server functionality, configuration, and logging.

### World Module
Zone management, NPC systems, and spawn mechanics.

## Development Guidelines

### Code Style
- Use meaningful variable and function names
- Follow consistent naming conventions (`rf_` prefix for public APIs)
- Include comprehensive documentation for all functions
- Use standard C types and avoid platform-specific types where possible

### Error Handling
- Use the `rf_result_t` enum for function return values
- Check all function results and handle errors appropriately
- Use the provided logging macros for error reporting

### Memory Management
- Use the provided memory management macros (`RF_MALLOC`, `RF_FREE`, etc.)
- Always check for null pointers
- Clean up resources in reverse order of allocation

## Contributing

1. Follow the established code style and conventions
2. Add comprehensive documentation for new functions
3. Include error handling and input validation
4. Test thoroughly in both Debug and Release configurations
5. Update this README if adding new modules or significant features

## License

[Specify your license here]

## Acknowledgments

- Original RF Online development team
- IDA Pro for decompilation capabilities
- Community contributors and testers

## Support

For questions, issues, or contributions, please [create an issue](link-to-issues) or contact the development team.

---

**Note**: This is a refactored version of decompiled code. Ensure you have proper rights and permissions before using this code in any commercial or production environment.
