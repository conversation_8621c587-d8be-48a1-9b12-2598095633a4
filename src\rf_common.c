/**
 * @file rf_common.c
 * @brief RF Online Common Utility Functions Implementation
 * <AUTHOR> from IDA Pro decompilation
 * @date 2025
 * 
 * This file implements common utility functions used throughout the
 * RF Online server codebase.
 */

#include "rf_common.h"
#include <stdarg.h>

#ifdef _WIN32
    #include <windows.h>
    #include <time.h>
#else
    #include <sys/time.h>
    #include <unistd.h>
#endif

/* ========================================================================
 * Global Variables
 * ======================================================================== */

static FILE* g_log_file = NULL;
static rf_mutex_t g_log_mutex;
static rf_bool_t g_log_initialized = false;

/* ========================================================================
 * Logging Functions
 * ======================================================================== */

/**
 * @brief Initialize the logging system
 * @param log_file Path to the log file (NULL for stdout only)
 */
void rf_log_init(const char* log_file)
{
    if (g_log_initialized) {
        return;
    }
    
    RF_MUTEX_INIT(&g_log_mutex);
    
    if (log_file) {
        g_log_file = fopen(log_file, "a");
        if (!g_log_file) {
            fprintf(stderr, "Warning: Could not open log file '%s', using stdout\n", log_file);
        }
    }
    
    g_log_initialized = true;
    
    /* Log initialization message */
    rf_log_message(RF_LOG_INFO, __FILE__, __LINE__, "Logging system initialized");
}

/**
 * @brief Cleanup the logging system
 */
void rf_log_cleanup(void)
{
    if (!g_log_initialized) {
        return;
    }
    
    rf_log_message(RF_LOG_INFO, __FILE__, __LINE__, "Logging system shutting down");
    
    RF_MUTEX_LOCK(&g_log_mutex);
    
    if (g_log_file) {
        fclose(g_log_file);
        g_log_file = NULL;
    }
    
    RF_MUTEX_UNLOCK(&g_log_mutex);
    RF_MUTEX_DESTROY(&g_log_mutex);
    
    g_log_initialized = false;
}

/**
 * @brief Log a message with specified level
 * @param level Log level
 * @param file Source file name
 * @param line Source line number
 * @param format Printf-style format string
 * @param ... Variable arguments
 */
void rf_log_message(rf_log_level_t level, const char* file, int line, 
                   const char* format, ...)
{
    if (!g_log_initialized) {
        return;
    }
    
    /* Get current timestamp */
    time_t now = time(NULL);
    struct tm* local_time = localtime(&now);
    
    /* Format timestamp */
    char timestamp[32];
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", local_time);
    
    /* Get log level string */
    const char* level_str;
    switch (level) {
        case RF_LOG_TRACE: level_str = "TRACE"; break;
        case RF_LOG_DEBUG: level_str = "DEBUG"; break;
        case RF_LOG_INFO:  level_str = "INFO "; break;
        case RF_LOG_WARN:  level_str = "WARN "; break;
        case RF_LOG_ERROR: level_str = "ERROR"; break;
        case RF_LOG_FATAL: level_str = "FATAL"; break;
        default:           level_str = "UNKN "; break;
    }
    
    /* Extract filename from full path */
    const char* filename = strrchr(file, '/');
    if (!filename) {
        filename = strrchr(file, '\\');
    }
    filename = filename ? filename + 1 : file;
    
    /* Format the message */
    va_list args;
    va_start(args, format);
    
    char message[1024];
    vsnprintf(message, sizeof(message), format, args);
    
    va_end(args);
    
    /* Thread-safe logging */
    RF_MUTEX_LOCK(&g_log_mutex);
    
    /* Log to file if available */
    if (g_log_file) {
        fprintf(g_log_file, "[%s] [%s] %s:%d - %s\n", 
               timestamp, level_str, filename, line, message);
        fflush(g_log_file);
    }
    
    /* Always log to console for errors and warnings */
    if (level >= RF_LOG_WARN || !g_log_file) {
        FILE* output = (level >= RF_LOG_ERROR) ? stderr : stdout;
        fprintf(output, "[%s] [%s] %s:%d - %s\n", 
               timestamp, level_str, filename, line, message);
        fflush(output);
    }
    
    RF_MUTEX_UNLOCK(&g_log_mutex);
}

/* ========================================================================
 * Memory Management Functions
 * ======================================================================== */

/**
 * @brief Safe malloc with error checking
 * @param size Size to allocate
 * @param file Source file name
 * @param line Source line number
 * @return Allocated memory or NULL on failure
 */
void* rf_safe_malloc(size_t size, const char* file, int line)
{
    if (size == 0) {
        rf_log_message(RF_LOG_WARN, file, line, "Attempted to allocate 0 bytes");
        return NULL;
    }
    
    void* ptr = malloc(size);
    if (!ptr) {
        rf_log_message(RF_LOG_ERROR, file, line, 
                      "Failed to allocate %zu bytes", size);
        return NULL;
    }
    
#ifdef _DEBUG
    rf_log_message(RF_LOG_TRACE, file, line, 
                  "Allocated %zu bytes at %p", size, ptr);
#endif
    
    return ptr;
}

/**
 * @brief Safe calloc with error checking
 * @param count Number of elements
 * @param size Size of each element
 * @param file Source file name
 * @param line Source line number
 * @return Allocated memory or NULL on failure
 */
void* rf_safe_calloc(size_t count, size_t size, const char* file, int line)
{
    if (count == 0 || size == 0) {
        rf_log_message(RF_LOG_WARN, file, line, 
                      "Attempted to allocate 0 elements or 0 size");
        return NULL;
    }
    
    void* ptr = calloc(count, size);
    if (!ptr) {
        rf_log_message(RF_LOG_ERROR, file, line, 
                      "Failed to allocate %zu elements of %zu bytes", count, size);
        return NULL;
    }
    
#ifdef _DEBUG
    rf_log_message(RF_LOG_TRACE, file, line, 
                  "Allocated %zu elements of %zu bytes at %p", count, size, ptr);
#endif
    
    return ptr;
}

/**
 * @brief Safe realloc with error checking
 * @param ptr Pointer to reallocate
 * @param size New size
 * @param file Source file name
 * @param line Source line number
 * @return Reallocated memory or NULL on failure
 */
void* rf_safe_realloc(void* ptr, size_t size, const char* file, int line)
{
    if (size == 0) {
        rf_log_message(RF_LOG_WARN, file, line, "Attempted to realloc to 0 bytes");
        free(ptr);
        return NULL;
    }
    
    void* new_ptr = realloc(ptr, size);
    if (!new_ptr) {
        rf_log_message(RF_LOG_ERROR, file, line, 
                      "Failed to reallocate %p to %zu bytes", ptr, size);
        return NULL;
    }
    
#ifdef _DEBUG
    rf_log_message(RF_LOG_TRACE, file, line, 
                  "Reallocated %p to %zu bytes at %p", ptr, size, new_ptr);
#endif
    
    return new_ptr;
}

/**
 * @brief Safe free with null pointer setting
 * @param ptr Pointer to pointer to free
 */
void rf_safe_free(void** ptr)
{
    if (ptr && *ptr) {
#ifdef _DEBUG
        rf_log_message(RF_LOG_TRACE, __FILE__, __LINE__, 
                      "Freeing memory at %p", *ptr);
#endif
        free(*ptr);
        *ptr = NULL;
    }
}

/* ========================================================================
 * Utility Functions
 * ======================================================================== */

/**
 * @brief Get current timestamp in milliseconds
 * @return Current timestamp
 */
rf_timestamp_t rf_get_timestamp(void)
{
#ifdef _WIN32
    return (rf_timestamp_t)time(NULL);
#else
    struct timeval tv;
    gettimeofday(&tv, NULL);
    return (rf_timestamp_t)(tv.tv_sec * 1000 + tv.tv_usec / 1000);
#endif
}

/**
 * @brief Get system tick count in milliseconds
 * @return Tick count
 */
rf_duration_t rf_get_tick_count(void)
{
#ifdef _WIN32
    return (rf_duration_t)GetTickCount64();
#else
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (rf_duration_t)(ts.tv_sec * 1000 + ts.tv_nsec / 1000000);
#endif
}

/**
 * @brief Sleep for specified milliseconds
 * @param milliseconds Duration to sleep
 */
void rf_sleep(rf_duration_t milliseconds)
{
#ifdef _WIN32
    Sleep((DWORD)milliseconds);
#else
    usleep(milliseconds * 1000);
#endif
}

/* ========================================================================
 * String Utility Functions
 * ======================================================================== */

/**
 * @brief Duplicate a string
 * @param str String to duplicate
 * @return Duplicated string (must be freed) or NULL on failure
 */
char* rf_string_duplicate(const char* str)
{
    if (!str) {
        return NULL;
    }
    
    size_t len = strlen(str) + 1;
    char* dup = RF_MALLOC(len);
    if (dup) {
        memcpy(dup, str, len);
    }
    
    return dup;
}

/**
 * @brief Trim whitespace from string
 * @param str String to trim (modified in place)
 */
void rf_string_trim(char* str)
{
    if (!str) {
        return;
    }
    
    /* Trim leading whitespace */
    char* start = str;
    while (*start && isspace((unsigned char)*start)) {
        start++;
    }
    
    /* Move string to beginning if needed */
    if (start != str) {
        memmove(str, start, strlen(start) + 1);
    }
    
    /* Trim trailing whitespace */
    char* end = str + strlen(str) - 1;
    while (end > str && isspace((unsigned char)*end)) {
        *end = '\0';
        end--;
    }
}

/**
 * @brief Compare strings ignoring case
 * @param str1 First string
 * @param str2 Second string
 * @return true if strings are equal (ignoring case)
 */
rf_bool_t rf_string_equals_ignore_case(const char* str1, const char* str2)
{
    if (!str1 || !str2) {
        return str1 == str2;
    }
    
#ifdef _WIN32
    return _stricmp(str1, str2) == 0;
#else
    return strcasecmp(str1, str2) == 0;
#endif
}
