# RF Online Server Configuration File
# ====================================
# 
# This file contains the main configuration settings for the RF Online server.
# Lines starting with # are comments and are ignored.
# 
# Format: key = value
# Boolean values: true/false, 1/0, yes/no
# Numeric values: integers or floating point numbers
# String values: enclosed in quotes if they contain spaces

# ========================================================================
# Server Information
# ========================================================================

# Server name displayed to clients
server_name = "RF Online Server - Refactored Edition"

# Server description
server_description = "Modernized RF Online server based on refactored decompiled code"

# Server version information
server_version_major = 1
server_version_minor = 0
server_version_patch = 0

# ========================================================================
# Network Configuration
# ========================================================================

# Primary server listening port
server_port = 27015

# Maximum number of concurrent client connections
max_clients = 1000

# Network buffer size (bytes)
network_buffer_size = 16384

# Maximum packet size (bytes)
max_packet_size = 8192

# Connection timeout (milliseconds)
connection_timeout = 30000

# Heartbeat interval (milliseconds)
heartbeat_interval = 30000

# Enable packet encryption
enable_encryption = true

# ========================================================================
# Authentication Settings
# ========================================================================

# Maximum login attempts before account lockout
max_login_attempts = 5

# Account lockout duration (milliseconds)
lockout_duration = 300000

# Session timeout (milliseconds)
session_timeout = 3600000

# Enable billing system integration
enable_billing = true

# Billing check interval (milliseconds)
billing_check_interval = 60000

# ========================================================================
# Database Configuration
# ========================================================================

# Database connection string
# Format: server=hostname;database=dbname;uid=username;pwd=password
database_connection = "server=localhost;database=rfonline;uid=rfserver;pwd=password123"

# Maximum database connections in pool
database_max_connections = 10

# Database connection timeout (milliseconds)
database_timeout = 5000

# Enable database connection pooling
database_pooling = true

# Database query timeout (milliseconds)
database_query_timeout = 30000

# ========================================================================
# Game World Settings
# ========================================================================

# Maximum number of zones
max_zones = 32

# Maximum NPCs per zone
max_npcs_per_zone = 500

# Maximum items per player
max_items_per_player = 200

# Player respawn time (milliseconds)
player_respawn_time = 30000

# Maximum player level
max_player_level = 99

# Maximum stat value
max_stat_value = 999

# ========================================================================
# Security Settings
# ========================================================================

# Enable anti-cheat system
enable_anti_cheat = true

# Anti-cheat check interval (milliseconds)
anti_cheat_interval = 5000

# Enable IP banning
enable_ip_banning = true

# Maximum failed connections before IP ban
max_failed_connections = 10

# IP ban duration (milliseconds)
ip_ban_duration = 3600000

# Enable packet validation
enable_packet_validation = true

# ========================================================================
# Performance Settings
# ========================================================================

# Number of worker threads
worker_threads = 4

# Enable multi-threading
enable_multithreading = true

# Update frequency (updates per second)
update_frequency = 60

# Enable performance monitoring
enable_performance_monitoring = true

# Performance log interval (milliseconds)
performance_log_interval = 60000

# ========================================================================
# Logging Configuration
# ========================================================================

# Log file path
log_file = "logs/server.log"

# Log level (TRACE, DEBUG, INFO, WARN, ERROR, FATAL)
log_level = "INFO"

# Enable console logging
enable_console_logging = true

# Enable file logging
enable_file_logging = true

# Maximum log file size (bytes)
max_log_file_size = 10485760

# Number of log files to keep
max_log_files = 10

# Log rotation enabled
enable_log_rotation = true

# ========================================================================
# Debug Settings
# ========================================================================

# Enable debug mode (only for development)
debug_mode = false

# Enable memory leak detection
enable_memory_leak_detection = false

# Enable detailed packet logging
enable_packet_logging = false

# Enable performance profiling
enable_profiling = false

# Debug log level (overrides log_level when debug_mode is true)
debug_log_level = "TRACE"

# ========================================================================
# Maintenance Settings
# ========================================================================

# Enable maintenance mode
maintenance_mode = false

# Maintenance message displayed to clients
maintenance_message = "Server is currently under maintenance. Please try again later."

# Scheduled maintenance time (24-hour format: HH:MM)
scheduled_maintenance_time = "03:00"

# Maintenance duration (minutes)
maintenance_duration = 60

# Enable automatic restarts
enable_auto_restart = false

# Auto restart time (24-hour format: HH:MM)
auto_restart_time = "04:00"

# ========================================================================
# Economy Settings
# ========================================================================

# Enable trading system
enable_trading = true

# Enable auction house
enable_auction_house = true

# Auction house fee percentage (0.0 - 1.0)
auction_house_fee = 0.05

# Maximum trade value
max_trade_value = 999999999

# Currency exchange rate (if multiple currencies)
currency_exchange_rate = 1.0

# ========================================================================
# Combat Settings
# ========================================================================

# Enable PvP (Player vs Player)
enable_pvp = true

# Enable PvE (Player vs Environment)
enable_pve = true

# Combat timeout (milliseconds)
combat_timeout = 30000

# Critical hit rate multiplier
critical_hit_multiplier = 2.0

# Experience rate multiplier
experience_rate = 1.0

# Drop rate multiplier
drop_rate = 1.0

# ========================================================================
# Advanced Settings
# ========================================================================

# Enable experimental features
enable_experimental_features = false

# Custom game rules file
custom_rules_file = "config/custom_rules.cfg"

# Plugin directory
plugin_directory = "plugins/"

# Enable plugin system
enable_plugins = false

# Configuration reload interval (milliseconds, 0 = disabled)
config_reload_interval = 0

# Enable remote administration
enable_remote_admin = false

# Remote admin port
remote_admin_port = 27016

# Remote admin password
remote_admin_password = "admin123"
