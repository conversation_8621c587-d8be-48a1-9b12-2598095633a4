<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  
  <!-- Filter Definitions -->
  <ItemGroup>
    <!-- Header File Filters -->
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Header Files\Core">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-111111111111}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files\Modules">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-222222222222}</UniqueIdentifier>
    </Filter>
    
    <!-- Source File Filters -->
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Source Files\Core">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-333333333333}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Authentication">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-444444444444}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Combat">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-555555555555}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Database">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-666666666666}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Economy">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-777777777777}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Item">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-888888888888}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Network">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-999999999999}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Player">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-AAAAAAAAAAAA}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\Security">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-BBBBBBBBBBBB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\System">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-CCCCCCCCCCCC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files\World">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-DDDDDDDDDDDD}</UniqueIdentifier>
    </Filter>
    
    <!-- Resource File Filters -->
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Resource Files\Documentation">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-EEEEEEEEEEEE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files\Configuration">
      <UniqueIdentifier>{A1B2C3D4-E5F6-7890-ABCD-FFFFFFFFFFFF}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  
  <!-- Header File Assignments -->
  <ItemGroup>
    <!-- Core Headers -->
    <ClInclude Include="include\rf_common.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    <ClInclude Include="include\rf_types.h">
      <Filter>Header Files\Core</Filter>
    </ClInclude>
    
    <!-- Module Headers -->
    <ClInclude Include="include\authentication.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\combat.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\database.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\economy.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\item.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\network.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\player.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\security.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\system.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
    <ClInclude Include="include\world.h">
      <Filter>Header Files\Modules</Filter>
    </ClInclude>
  </ItemGroup>
  
  <!-- Source File Assignments -->
  <ItemGroup>
    <!-- Core Source Files -->
    <ClCompile Include="src\main.c">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    <ClCompile Include="src\rf_common.c">
      <Filter>Source Files\Core</Filter>
    </ClCompile>
    
    <!-- Authentication Module -->
    <ClCompile Include="src\authentication\auth_manager.c">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\billing_manager.c">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="src\authentication\session_manager.c">
      <Filter>Source Files\Authentication</Filter>
    </ClCompile>
    
    <!-- Combat Module -->
    <ClCompile Include="src\combat\combat_system.c">
      <Filter>Source Files\Combat</Filter>
    </ClCompile>
    <ClCompile Include="src\combat\skill_system.c">
      <Filter>Source Files\Combat</Filter>
    </ClCompile>
    <ClCompile Include="src\combat\damage_calculator.c">
      <Filter>Source Files\Combat</Filter>
    </ClCompile>
    
    <!-- Database Module -->
    <ClCompile Include="src\database\database_manager.c">
      <Filter>Source Files\Database</Filter>
    </ClCompile>
    <ClCompile Include="src\database\query_builder.c">
      <Filter>Source Files\Database</Filter>
    </ClCompile>
    <ClCompile Include="src\database\connection_pool.c">
      <Filter>Source Files\Database</Filter>
    </ClCompile>
    
    <!-- Economy Module -->
    <ClCompile Include="src\economy\trade_system.c">
      <Filter>Source Files\Economy</Filter>
    </ClCompile>
    <ClCompile Include="src\economy\auction_house.c">
      <Filter>Source Files\Economy</Filter>
    </ClCompile>
    <ClCompile Include="src\economy\currency_manager.c">
      <Filter>Source Files\Economy</Filter>
    </ClCompile>
    
    <!-- Item Module -->
    <ClCompile Include="src\item\item_manager.c">
      <Filter>Source Files\Item</Filter>
    </ClCompile>
    <ClCompile Include="src\item\item_factory.c">
      <Filter>Source Files\Item</Filter>
    </ClCompile>
    <ClCompile Include="src\item\enhancement_system.c">
      <Filter>Source Files\Item</Filter>
    </ClCompile>
    
    <!-- Network Module -->
    <ClCompile Include="src\network\net_socket_connect.c">
      <Filter>Source Files\Network</Filter>
    </ClCompile>
    <ClCompile Include="src\network\network_server.c">
      <Filter>Source Files\Network</Filter>
    </ClCompile>
    <ClCompile Include="src\network\packet_handler.c">
      <Filter>Source Files\Network</Filter>
    </ClCompile>
    
    <!-- Player Module -->
    <ClCompile Include="src\player\player_manager.c">
      <Filter>Source Files\Player</Filter>
    </ClCompile>
    <ClCompile Include="src\player\character_data.c">
      <Filter>Source Files\Player</Filter>
    </ClCompile>
    <ClCompile Include="src\player\inventory_manager.c">
      <Filter>Source Files\Player</Filter>
    </ClCompile>
    
    <!-- Security Module -->
    <ClCompile Include="src\security\anti_cheat.c">
      <Filter>Source Files\Security</Filter>
    </ClCompile>
    <ClCompile Include="src\security\encryption.c">
      <Filter>Source Files\Security</Filter>
    </ClCompile>
    <ClCompile Include="src\security\access_control.c">
      <Filter>Source Files\Security</Filter>
    </ClCompile>
    
    <!-- System Module -->
    <ClCompile Include="src\system\server_core.c">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    <ClCompile Include="src\system\configuration.c">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    <ClCompile Include="src\system\logging.c">
      <Filter>Source Files\System</Filter>
    </ClCompile>
    
    <!-- World Module -->
    <ClCompile Include="src\world\zone_manager.c">
      <Filter>Source Files\World</Filter>
    </ClCompile>
    <ClCompile Include="src\world\npc_manager.c">
      <Filter>Source Files\World</Filter>
    </ClCompile>
    <ClCompile Include="src\world\spawn_system.c">
      <Filter>Source Files\World</Filter>
    </ClCompile>
  </ItemGroup>
  
  <!-- Resource File Assignments -->
  <ItemGroup>
    <!-- Documentation -->
    <None Include="README.md">
      <Filter>Resource Files\Documentation</Filter>
    </None>
    <None Include="CHANGELOG.md">
      <Filter>Resource Files\Documentation</Filter>
    </None>
    <None Include="LICENSE">
      <Filter>Resource Files\Documentation</Filter>
    </None>
    <Text Include="docs\REFACTORING_NOTES.txt">
      <Filter>Resource Files\Documentation</Filter>
    </Text>
    
    <!-- Configuration -->
    <Text Include="config\server.cfg">
      <Filter>Resource Files\Configuration</Filter>
    </Text>
  </ItemGroup>
  
</Project>
